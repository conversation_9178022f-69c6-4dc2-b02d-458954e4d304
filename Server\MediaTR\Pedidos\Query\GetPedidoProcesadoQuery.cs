using DevExpress.CodeParser;
using MediatR;
using Microsoft.Data.SqlClient;
using Microsoft.EntityFrameworkCore;
using Nelibur.ObjectMapper;
using ProgramadorGeneralBLZ.Server.Controllers;
using ProgramadorGeneralBLZ.Server.Data.DatoLita01;
using ProgramadorGeneralBLZ.Server.Data.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Models.DatoLita01;
using ProgramadorGeneralBLZ.Server.Models.ProgramadorLitalsa;
using ProgramadorGeneralBLZ.Server.Services;
using ProgramadorGeneralBLZ.Server.Services.DatabaseManipulation;
using ProgramadorGeneralBLZ.Shared;
using ProgramadorGeneralBLZ.Shared.DTO;
using ProgramadorGeneralBLZ.Shared.ResponseModels;
using System.Data;
using System.Data.Odbc;
using System.Data.OleDb;
using System.Linq;
using System.Reflection;

namespace ProgramadorGeneralBLZ.Server.MediaTR.Pedidos.Query
{
    public class GetPedidoProcesadoQuery : IRequest<ListResult<PedidoProcesadoLiteDTO>>
    {
        public FiltroDTO Filtro;

        public GetPedidoProcesadoQuery(FiltroDTO filtro)
        {
            Filtro = filtro;
        }
    }

    public class GetPedidoProcesadoQueryHandler : IRequestHandler<GetPedidoProcesadoQuery, ListResult<PedidoProcesadoLiteDTO>>
    {
        private readonly ProgramadorLitalsaContext _contextProg;
        private readonly DatoLita01Context _datoLita01Context;
        private readonly IConfiguration _configuration;
        private readonly ILogger<GestionTablasController> _logger;
        private readonly IDataManipulationService _dataManipulationService;

        public GetPedidoProcesadoQueryHandler(ProgramadorLitalsaContext contextProg, IConfiguration configuration,
            ILogger<GestionTablasController> logger, IDataManipulationService dataManipulationService, DatoLita01Context datoLita01Context)
        {
            _contextProg = contextProg;
            _configuration = configuration;
            _logger = logger;
            _dataManipulationService = dataManipulationService;
            _datoLita01Context = datoLita01Context;
        }

        /// <summary>
        /// Recuperamos de la BD los datos del pedido indicado.
        /// </summary>
        /// <param name="request"></param>
        /// <param name="cancellationToken"></param>
        /// <returns></returns>
        //public async Task<ListResult<PedidoProcesadoLiteDTO>> Handle(GetPedidoProcesadoQuery request, CancellationToken cancellationToken)
        //{
        //    var result = new ListResult<PedidoProcesadoLiteDTO>
        //    {
        //        Data = new List<PedidoProcesadoLiteDTO>(),
        //        Errors = new List<string>()
        //    };
        //    try
        //    {
        //        var connString = _configuration.GetConnectionString("AccessConnectionStringODBC");
        //        var filtro = request.Filtro;
        //        var hayLote = !string.IsNullOrEmpty(filtro.LoteBarniz);
        //        var paramsArray = !hayLote
        //            ? ConstruirArrayParametros(filtro)
        //            : null;

        //        if (hayLote)
        //        {
        //            DateTime? fechaInicio = null;
        //            DateTime? fechaFin = null;
        //            int? barniz = null;
        //            // Consulta para obtener el barniz a partir de un lote
        //            var queryBarniz = """
        //                              SELECT TOP 1 IDPRODUCTO as Barniz
        //                              FROM LOTESNODRIZAS
        //                              WHERE LOTE = ?
        //                              """;
        //            // Consulta para obtener la fecha de inicio
        //            var queryInicio = """
        //                              SELECT MIN(HORA) AS FechaInicio
        //                              FROM LOTESNODRIZAS
        //                              WHERE LOTE = ?
        //                              """;

        //            // Consulta para obtener la fecha de fin
        //            var queryFin = """
        //                           SELECT MAX(HORA) AS FechaFin
        //                           FROM LOTESNODRIZAS
        //                           WHERE LOTE = ? AND FECHA > (SELECT MIN(HORA) FROM LOTESNODRIZAS WHERE LOTE = ?)
        //                           """;

        //            using (var connection = new OdbcConnection(connString))
        //            {
        //                await connection.OpenAsync(cancellationToken);

        //                // Obtener el barniz
        //                using (var commandBarniz = new OdbcCommand(queryBarniz, connection))
        //                {
        //                    commandBarniz.Parameters.AddWithValue("@LOTE", filtro.LoteBarniz);
        //                    var resultBarniz = await commandBarniz.ExecuteScalarAsync(cancellationToken);
        //                    barniz = int.Parse(resultBarniz?.ToString() ?? "");
        //                }

        //                // Obtener fecha de inicio
        //                using (var commandInicio = new OdbcCommand(queryInicio, connection))
        //                {
        //                    commandInicio.Parameters.AddWithValue("@LOTE", filtro.LoteBarniz);
        //                    fechaInicio = (await commandInicio.ExecuteScalarAsync(cancellationToken)) as DateTime?;
        //                }

        //                // Obtener fecha de fin
        //                using (var commandFin = new OdbcCommand(queryFin, connection))
        //                {
        //                    commandFin.Parameters.AddWithValue("@LOTE", filtro.LoteBarniz);
        //                    commandFin.Parameters.AddWithValue("@LOTE", filtro.LoteBarniz); // Se repite porque la consulta lo utiliza dos veces
        //                    fechaFin = (await commandFin.ExecuteScalarAsync(cancellationToken)) as DateTime?;
        //                }
        //            }

        //            filtro.FechaMinPedido = fechaInicio;
        //            filtro.FechaMaxPedido = fechaFin;
        //            filtro.CodBarniz = barniz;
        //            paramsArray = ConstruirArrayParametros(filtro);
        //        }
        //        var pedidos = await _contextProg.Set<PedidoProcesadoLiteDTO>()
        //            .FromSqlRaw("[dbo].[sp_ConsultaGetAllPedidos] @IdCliente, @IdPedido, @Pi1ped, @Pi2ped, @Pi3ped, @Pe1ped, " +
        //                        "@Pe2ped, @Pe3ped, @FechaPedido, @FechaMinPedido, @FechaMaxPedido, @TipoPedido, @Motivos, @TipoElemento, @Formato, @Plano, " +
        //                        "@AnchoHjlta, @LargoHjlta, @EspesorHjlta, @TipoHjlta, @EstadoTintas, @LineaTintas, @ObsArticulo, " +
        //                        "@Tinta1, @Tinta2, @Tinta3, @Tinta4, @Tinta5, @Tinta6, @Tinta7, @Cd1ped, @Cd2ped, @Cd3ped, @Cd4ped, " +
        //                        "@CodApli, @CodBarniz, @LoteBarniz, @ListBarniz, @OcultarReprocesos, @SuPedido, @WO",
        //                paramsArray)
        //            .ToListAsync(cancellationToken);

        //        // Llamar al procedimiento almacenado con los parámetros individuales
        //        if (filtro.IncluirLote)
        //        {
        //            _logger.Log(LogLevel.Information, "TRAZA-   ENTRAMOS EN IncluirLote");
        //            foreach (var p in pedidos)
        //            {
        //                if (p.Barniz is not > 0)
        //                    continue;

        //                var fecha = p.FechaPedido.Value.Date.ToString("dd/MM/yyyy");
        //                var hora = p.FechaPedido.Value.TimeOfDay.ToString();
        //                var idProducto = p.Barniz.Value.ToString();
        //                //Hora en verdad es dia y hora, pero al visualizarlo en el access tiene máscara de formato para mostrar solo hora.
        //                //Por eso sólo filtramos por la hora pasando el datetime completo de FechaPedido
        //                //Esta consulta me devuelve el primer lote activo, es decir el primer registro con una fecha inferior a la fecha del pedido.
        //                //POR CIERTO TODO ESTO SE IRÁ A TOMAR POR SACO EN CUANTO ARRANQUE EL PROGRAMA DE APQ NUEVO... Y LUEGO MORIRÁ CON SAP :}
        //                List<string> lotes = new List<string>();
        //                var query = """
        //                            SELECT TOP 1 LOTE
        //                            FROM LOTESNODRIZAS
        //                            WHERE HORA <= ? AND IDPRODUCTO = ?
        //                            ORDER BY FECHA DESC, HORA DESC
        //                            """;
        //                using (var connection = new OdbcConnection(connString))
        //                {
        //                    await connection.OpenAsync(cancellationToken);
        //                    using (var command = new OdbcCommand(query, connection))
        //                    {
        //                        //command.Parameters.Add(new OdbcParameter("@Fecha", fecha));
        //                        command.Parameters.Add(new OdbcParameter("@Hora", p.FechaPedido.Value));
        //                        command.Parameters.Add(new OdbcParameter("@IdProducto", idProducto));

        //                        var result2 = await command.ExecuteScalarAsync(cancellationToken);
        //                        lotes.Add(result2?.ToString());

        //                        _logger.Log(LogLevel.Information, $"TRAZA-   ENTRAMOS EN PRIMERA CONSULTA LOTE: {result2?.ToString()}");
        //                    }
        //                }

        //                var query2 = """
        //                            SELECT DISTINCT LOTE
        //                            FROM LOTESNODRIZAS
        //                            WHERE (HORA >= ? AND HORA <= ? ) AND IDPRODUCTO = ?
        //                            """;

        //                using (var connection = new OdbcConnection(connString))
        //                {
        //                    await connection.OpenAsync(cancellationToken);
        //                    using (var command = new OdbcCommand(query2, connection))
        //                    {
        //                        command.Parameters.Add(new OdbcParameter("@Hora", p.FechaPedido.Value));
        //                        command.Parameters.Add(new OdbcParameter("@Hora", p.FechaFin.Value));
        //                        command.Parameters.Add(new OdbcParameter("@IdProducto", idProducto));

        //                        using (var reader = await command.ExecuteReaderAsync(cancellationToken))
        //                        {
        //                            while (await reader.ReadAsync(cancellationToken))
        //                            {
        //                                lotes.Add(reader.GetString(0));
        //                                _logger.Log(LogLevel.Information, $"TRAZA-   ENTRAMOS EN SEGUNDA CONSULTA LOTE: {reader.GetString(0)}");
        //                            }
        //                        }
        //                    }
        //                }

        //                p.Lote = string.Join(" || ", lotes.Distinct());
        //                var textoTuberia = $"_TUBERIA/{p.Barniz}";
        //                p.Tuberia = await _contextProg.HojasTrabajoG21.AnyAsync(o => o.Orden == p.IdPedido && o.Cantidad > 0 && o.Dato.Contains(textoTuberia), cancellationToken);
        //            }
        //        }

        //        result.Data = pedidos;
        //        return result;
        //    }
        //    catch (Exception e)
        //    {
        //        result.Errors.Add($"GetPedidoProcesadoQueryHandler: {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}");
        //        return result;
        //    }
        //}

        public async Task<ListResult<PedidoProcesadoLiteDTO>> Handle(GetPedidoProcesadoQuery request, CancellationToken cancellationToken)
        {
            var result = new ListResult<PedidoProcesadoLiteDTO>
            {
                Data = new List<PedidoProcesadoLiteDTO>(),
                Errors = new List<string>()
            };
            try
            {
                var filtro = request.Filtro;
                filtro.LoteBarniz = filtro.LoteBarniz?.Trim();
                var hayLote = !string.IsNullOrEmpty(filtro.LoteBarniz);
                var paramsArray = !hayLote
                    ? ConstruirArrayParametros(filtro)
                    : null;

                if (hayLote)
                {
                    // Consulta para obtener el barniz a partir de un lote
                    var loteApq = await _contextProg.ApqLotesnodrizas
                        .AsNoTracking()
                        .FirstOrDefaultAsync(l => l.Lote.Trim().Equals(filtro.LoteBarniz));
                    filtro.CodBarniz = (int?)loteApq.Idproducto;
                    paramsArray = ConstruirArrayParametros(filtro);
                }

                // Llamar al procedimiento almacenado con los parámetros individuales - COMENTADO PARA NO USAR EF
                //var pedidos = await _contextProg.Set<PedidoProcesadoLiteDTO>()
                //    .FromSqlRaw("EXECUTE [dbo].[sp_ConsultaGetAllPedidos] @IdCliente, @IdPedido, @Pi1ped, @Pi2ped, @Pi3ped, @Pe1ped, " +
                //                "@Pe2ped, @Pe3ped, @FechaPedido, @FechaMinPedido, @FechaMaxPedido, @TipoPedido, @Motivos, @TipoElemento, @Formato, @Plano, " +
                //                "@AnchoHjlta, @LargoHjlta, @EspesorHjlta, @TipoHjlta, @EstadoTintas, @LineaTintas, @ObsArticulo, " +
                //                "@Tinta1, @Tinta2, @Tinta3, @Tinta4, @Tinta5, @Tinta6, @Tinta7, @Cd1ped, @Cd2ped, @Cd3ped, @Cd4ped, " +
                //                "@CodApli, @CodBarniz, @LoteBarniz, @ListBarniz, @OcultarReprocesos, @SuPedido, @WO",
                //        paramsArray)
                //    .ToListAsync(cancellationToken);

                // TEMPORAL: Usar EF para comparar rendimiento
                System.Diagnostics.Debug.WriteLine($"[DEBUG] Iniciando consulta con IncluirLote: {filtro.IncluirLote}");

                // Probar primero con Entity Framework para comparar
                var startTimeEF = DateTime.Now;
                var pedidosEF = await _contextProg.Set<PedidoProcesadoLiteDTO>()
                    .FromSqlRaw("EXECUTE [dbo].[sp_ConsultaGetAllPedidos] @IdCliente, @IdPedido, @Pi1ped, @Pi2ped, @Pi3ped, @Pe1ped, " +
                                "@Pe2ped, @Pe3ped, @FechaPedido, @FechaMinPedido, @FechaMaxPedido, @TipoPedido, @Motivos, @TipoElemento, @Formato, @Plano, " +
                                "@AnchoHjlta, @LargoHjlta, @EspesorHjlta, @TipoHjlta, @EstadoTintas, @LineaTintas, @ObsArticulo, " +
                                "@Tinta1, @Tinta2, @Tinta3, @Tinta4, @Tinta5, @Tinta6, @Tinta7, @Cd1ped, @Cd2ped, @Cd3ped, @Cd4ped, " +
                                "@CodApli, @CodBarniz, @LoteBarniz, @ListBarniz, @OcultarReprocesos, @SuPedido, @WO",
                        paramsArray)
                    .ToListAsync(cancellationToken);
                var endTimeEF = DateTime.Now;
                System.Diagnostics.Debug.WriteLine($"[DEBUG] EF ejecutado en {(endTimeEF - startTimeEF).TotalSeconds:F2} segundos, {pedidosEF.Count} registros");

                var pedidos = pedidosEF;

                // Llamada directa al stored procedure sin Entity Framework (comentada temporalmente)
                //var pedidos = await EjecutarStoredProcedureDirecto(paramsArray, cancellationToken);

                if (!filtro.IncluirLote)
                {
                    result.Data = pedidos;
                }
                else
                {
                    // Después de hablar con Álvaro, la idea es recuperar de los partes de trabajo aquellos lotes de barnices que no vayan por tubería.
                    // Para esto, recurrimos a la vista OBSERVA.
                    // Para los lotes tirados por tubería, lo que se hace es, como ha indicado Álvaro, coger el lote del barniz más próximo y anterior a la fecha que 
                    // indica el parte de trabajo. Es decir, sólo UNO.

                    // POR CIERTO TODO ESTO SE IRÁ A TOMAR POR SACO EN CUANTO ARRANQUE EL PROGRAMA DE APQ NUEVO... Y LUEGO MORIRÁ CON SAP :}
                    if (!hayLote)
                    {
                        // Precargamos los registros de LOTESNODRIZAS para el producto, para no hacer múltiples consultas al access y lastrar la consulta
                        // Hora en verdad es dia y hora, pero al visualizarlo en el access tiene máscara de formato para mostrar solo hora
                        // Cogemos nodriza > 0 para quitar los que son a máquina
                        var lotesNodrizasPrecargado = await _contextProg.ApqLotesnodrizas
                            .AsNoTracking()
                            .Where(x => x.Fecha.HasValue
                                        && x.Hora.HasValue
                                        && !string.IsNullOrEmpty(x.Lote)
                                        && x.Nodriza.HasValue
                                        && x.Nodriza.Value > 0)
                            .OrderByDescending(x => x.Hora)
                            .ToListAsync();
                        var lotesNodrizasPrecargadoDTO = TinyMapper.Map<List<ApqLotesnodrizasDTO>>(lotesNodrizasPrecargado);

                        foreach (var p in pedidos)
                        {
                            if (p.Barniz is not > 0)
                                continue;

                            var idProducto = p.Barniz.Value;

                            // con esta query y su posterior proceso, si hay lotes repetidos para una misma orden... únicamente saca el más antiguo segun fechaini y horaini
                            // esto es imprescindible para sacar el lote por tubería oportuno, porque necesitamos el registro más antiguo
                            var listaPartes = await _datoLita01Context.Observa
                                .Where(o => o.Orden.Equals(p.IdPedido.ToString())
                                            && o.Cantidad > 0
                                            && o.Barniz.Trim().Equals(idProducto.ToString())
                                            && o.Fechaini.HasValue
                                            && !string.IsNullOrEmpty(o.Horaini)
                                            && !string.IsNullOrEmpty(o.Lote)
                                            && !string.IsNullOrEmpty(o.Barniz))
                                .ToListAsync(cancellationToken);
                            listaPartes = listaPartes
                                .GroupBy(o => o.Lote)
                                .Select(group => group
                                    .OrderBy(o => o.Fechaini)  // Primero por fecha (más antigua)
                                    .ThenBy(o => TimeSpan.Parse(o.Horaini))  // Luego por hora (más temprana)
                                    .First())
                                .ToList();

                            if (listaPartes.Any())
                            {
                                var textoTuberia = $"_TUBERIA/{p.Barniz}";
                                p.Tuberia = listaPartes.Select(o => o.Lote.Trim()).First().Equals(textoTuberia); // en principio, un lote si va por tuberia, solo va por tuberia...

                                if (!p.Tuberia)
                                {
                                    p.Lote = string.Join(" || ", listaPartes.Select(o => o.Lote.Trim()).Distinct());
                                }
                                else // si va por tubería, hay que consultar el access
                                {
                                    var listaLotes = new List<string>();
                                    var lotesNodrizasDto = lotesNodrizasPrecargadoDTO
                                        .Where(x => x.Idproducto == idProducto)
                                        .ToList();

                                    foreach (var parteTrabajo in listaPartes)
                                    {
                                        // se coge el primero con fechaHora inferior al del parte de trabajo
                                        // cuando se hace la query al access, ya se ordena por fechaHora, osea que no hace falta ordenar aquí
                                        var fechaHoraCompleta = parteTrabajo.Fechaini.Value.Add(TimeSpan.Parse(parteTrabajo.Horaini));
                                        var lote = lotesNodrizasDto
                                            .Where(l => l.Hora <= fechaHoraCompleta)
                                            .FirstOrDefault();

                                        listaLotes.Add(lote.Lote);
                                    }
                                    p.Lote = string.Join(" || ", listaLotes);
                                }
                            }
                        }

                        result.Data = pedidos;
                    }
                    else
                    {
                        // Precargamos los registros de LOTESNODRIZAS para el producto, para no hacer múltiples consultas al access y lastrar la consulta
                        // Hora en verdad es dia y hora, pero al visualizarlo en el access tiene máscara de formato para mostrar solo hora
                        // Cogemos nodriza > 0 para quitar los que son a máquina
                        var lotesNodrizasPrecargado = await _contextProg.ApqLotesnodrizas
                            .AsNoTracking()
                            .Where(x => x.Fecha.HasValue
                                        && x.Hora.HasValue
                                        && !string.IsNullOrEmpty(x.Lote)
                                        && (int?)x.Idproducto == filtro.CodBarniz
                                        && x.Nodriza.HasValue
                                        && x.Nodriza.Value > 0)
                            .OrderByDescending(x => x.Hora)
                            .ToListAsync();
                        var lotesNodrizasPrecargadoDTO = TinyMapper.Map<List<ApqLotesnodrizasDTO>>(lotesNodrizasPrecargado);

                        // Cargamos todos los partes de trabajo con barniz coincidente y cantidad > 0
                        var listaPartes = await _datoLita01Context.Observa
                            .Where(o => o.Cantidad > 0
                                        && o.Barniz.Trim().Equals(filtro.CodBarniz.ToString())
                                        && o.Fechaini.HasValue
                                        && !string.IsNullOrEmpty(o.Horaini)
                                        && !string.IsNullOrEmpty(o.Lote)
                                        && !string.IsNullOrEmpty(o.Barniz)
                                        && !string.IsNullOrEmpty(o.Orden))
                            .ToListAsync(cancellationToken);

                        var pedidosFiltrados = new List<PedidoProcesadoLiteDTO>();
                        var pedidosYaProcesados = new HashSet<int>(); // Para evitar duplicados

                        if (listaPartes.Any())
                        {
                            foreach (var parteTrabajo in listaPartes)
                            {
                                var esTuberia = parteTrabajo.Lote.Trim().Equals($"_TUBERIA/{filtro.CodBarniz}");
                                var pedido = pedidos.FirstOrDefault(p => p.IdPedido.ToString().Equals(parteTrabajo.Orden.Trim()));
                                if (pedido == null || pedidosYaProcesados.Contains(pedido.IdPedido.Value))
                                    continue;

                                if (!esTuberia)
                                {
                                    // Si no es tubería, comprobamos si el lote coincide
                                    var loteObsTrim = parteTrabajo.Lote?.Trim();

                                    if (loteObsTrim.Equals(filtro.LoteBarniz))
                                    {
                                        // Asignamos los valores al pedido original
                                        pedido.Lote = loteObsTrim;
                                        pedido.Tuberia = false;
                                        pedidosFiltrados.Add(pedido);
                                        pedidosYaProcesados.Add(pedido.IdPedido.Value);
                                    }
                                }
                                else
                                {
                                    var horaParsed = TimeSpan.TryParse(parteTrabajo.Horaini, out var parsedHora) ? parsedHora : TimeSpan.Zero;
                                    var fechaHora = parteTrabajo.Fechaini.Value.Add(parsedHora);

                                    // Si es tubería, consultamos la tabla precargada de LOTESNODRIZAS, que ya está ordenada por fechaHora y seleccionamos primer registro
                                    var loteApq = lotesNodrizasPrecargadoDTO
                                        .FirstOrDefault(l => l.Hora.HasValue
                                                             && l.Hora <= fechaHora);

                                    // Si el lote real es el indicado, lo añadimos
                                    if (loteApq.Lote.Trim().Equals(filtro.LoteBarniz))
                                    {
                                        pedido.Lote = loteApq.Lote;
                                        pedido.Tuberia = true;
                                        pedidosFiltrados.Add(pedido);
                                        pedidosYaProcesados.Add(pedido.IdPedido.Value);
                                    }
                                }
                            }

                        }
                        result.Data = pedidosFiltrados;
                    }
                }
            }
            catch (Exception e)
            {
                result.Errors.Add($"GetPedidoProcesadoQueryHandler: {e.Message}--{(!string.IsNullOrWhiteSpace(e.InnerException?.Message) ? e.InnerException : string.Empty)}");
                return result;
            }

            return result;
        }

        /// <summary>
        /// Ejecuta el stored procedure directamente sin Entity Framework
        /// </summary>
        /// <param name="parameters">Parámetros del stored procedure</param>
        /// <param name="cancellationToken">Token de cancelación</param>
        /// <returns>Lista de PedidoProcesadoLiteDTO</returns>
        private async Task<List<PedidoProcesadoLiteDTO>> EjecutarStoredProcedureDirecto(object[] parameters, CancellationToken cancellationToken)
        {
            var pedidos = new List<PedidoProcesadoLiteDTO>();

            // Obtener la cadena de conexión del contexto de Entity Framework
            var connectionString = _contextProg.Database.GetConnectionString();

            using (var connection = new SqlConnection(connectionString))
            {
                await connection.OpenAsync(cancellationToken);

                using (var command = new SqlCommand("[dbo].[sp_ConsultaGetAllPedidos]", connection))
                {
                    command.CommandType = CommandType.StoredProcedure;
                    command.CommandTimeout = 600; // Aumentar timeout a 10 minutos

                    // Añadir configuraciones para mejorar rendimiento
                    command.Connection.InfoMessage += (sender, e) => {
                        System.Diagnostics.Debug.WriteLine($"SQL Info: {e.Message}");
                    };

                    // Añadir parámetros al comando
                    if (parameters != null)
                    {
                        foreach (SqlParameter param in parameters)
                        {
                            command.Parameters.Add(param);
                            // Log de parámetros no nulos para debug
                            if (param.Value != DBNull.Value && param.Value != null)
                            {
                                System.Diagnostics.Debug.WriteLine($"[DEBUG] Parámetro {param.ParameterName}: {param.Value}");
                            }
                        }
                    }

                    System.Diagnostics.Debug.WriteLine("[DEBUG] Ejecutando stored procedure...");
                    var startTime = DateTime.Now;

                    using (var reader = await command.ExecuteReaderAsync(cancellationToken))
                    {
                        while (await reader.ReadAsync(cancellationToken))
                        {
                            var pedido = new PedidoProcesadoLiteDTO();

                            // Mapear todos los campos de PedidoProcesado que devuelve el SP
                            pedido.Id = reader.IsDBNull("Id") ? 0 : reader.GetInt32("Id");
                            pedido.IdPedido = reader.IsDBNull("IdPedido") ? null : reader.GetInt32("IdPedido");
                            pedido.Supedido = reader.IsDBNull("Supedido") ? null : reader.GetString("Supedido");
                            pedido.IdCliente = reader.IsDBNull("IdCliente") ? null : reader.GetInt32("IdCliente");
                            pedido.FechaPedido = reader.IsDBNull("FechaPedido") ? null : reader.GetDateTime("FechaPedido");
                            pedido.RequeridoEnFecha = reader.IsDBNull("RequeridoEnFecha") ? null : reader.GetDateTime("RequeridoEnFecha");
                            pedido.ActualizacionFechaRequerida = reader.IsDBNull("ActualizacionFechaRequerida") ? null : reader.GetDateTime("ActualizacionFechaRequerida");
                            pedido.HojasPedido = reader.IsDBNull("HojasPedido") ? null : reader.GetInt32("HojasPedido");
                            pedido.HojasTerminadas = reader.IsDBNull("HojasTerminadas") ? null : reader.GetInt32("HojasTerminadas");
                            pedido.HojasLlevadas = reader.IsDBNull("HojasLlevadas") ? null : reader.GetInt32("HojasLlevadas");
                            pedido.Motivos = reader.IsDBNull("Motivos") ? null : reader.GetString("Motivos");
                            pedido.TipoElemento = reader.IsDBNull("TipoElemento") ? null : reader.GetString("TipoElemento");
                            pedido.Formato = reader.IsDBNull("Formato") ? null : reader.GetFloat("Formato");
                            pedido.Plano = reader.IsDBNull("Plano") ? null : reader.GetString("Plano");
                            pedido.Pi1ped = reader.IsDBNull("Pi1ped") ? null : reader.GetInt32("Pi1ped");
                            pedido.Hojaspi1ped = reader.IsDBNull("Hojaspi1ped") ? null : reader.GetInt32("Hojaspi1ped");
                            pedido.Pi2ped = reader.IsDBNull("Pi2ped") ? null : reader.GetInt32("Pi2ped");
                            pedido.Hojaspi2ped = reader.IsDBNull("Hojaspi2ped") ? null : reader.GetInt32("Hojaspi2ped");
                            pedido.Pi3ped = reader.IsDBNull("Pi3ped") ? null : reader.GetInt32("Pi3ped");
                            pedido.Hojaspi3ped = reader.IsDBNull("Hojaspi3ped") ? null : reader.GetInt32("Hojaspi3ped");
                            pedido.Pe1ped = reader.IsDBNull("Pe1ped") ? null : reader.GetInt32("Pe1ped");
                            pedido.Hojaspe1ped = reader.IsDBNull("Hojaspe1ped") ? null : reader.GetInt32("Hojaspe1ped");
                            pedido.Pe2ped = reader.IsDBNull("Pe2ped") ? null : reader.GetInt32("Pe2ped");
                            pedido.Hojaspe2ped = reader.IsDBNull("Hojaspe2ped") ? null : reader.GetInt32("Hojaspe2ped");
                            pedido.Pe3ped = reader.IsDBNull("Pe3ped") ? null : reader.GetInt32("Pe3ped");
                            pedido.Hojaspe3ped = reader.IsDBNull("Hojaspe3ped") ? null : reader.GetInt32("Hojaspe3ped");

                            // Campos C01ped a Co11ped (tintas)
                            pedido.C01ped = reader.IsDBNull("C01ped") ? null : reader.GetInt32("C01ped");
                            pedido.Hojasco1ped = reader.IsDBNull("Hojasco1ped") ? null : reader.GetInt32("Hojasco1ped");
                            pedido.C02ped = reader.IsDBNull("C02ped") ? null : reader.GetInt32("C02ped");
                            pedido.Hojasco2ped = reader.IsDBNull("Hojasco2ped") ? null : reader.GetInt32("Hojasco2ped");
                            pedido.C03ped = reader.IsDBNull("C03ped") ? null : reader.GetInt32("C03ped");
                            pedido.Hojasco3ped = reader.IsDBNull("Hojasco3ped") ? null : reader.GetInt32("Hojasco3ped");
                            pedido.C04ped = reader.IsDBNull("C04ped") ? null : reader.GetInt32("C04ped");
                            pedido.Hojasco4ped = reader.IsDBNull("Hojasco4ped") ? null : reader.GetInt32("Hojasco4ped");
                            pedido.C05ped = reader.IsDBNull("C05ped") ? null : reader.GetInt32("C05ped");
                            pedido.Hojasco5ped = reader.IsDBNull("Hojasco5ped") ? null : reader.GetInt32("Hojasco5ped");
                            pedido.C06ped = reader.IsDBNull("C06ped") ? null : reader.GetInt32("C06ped");
                            pedido.Hojasco6ped = reader.IsDBNull("Hojasco6ped") ? null : reader.GetInt32("Hojasco6ped");
                            pedido.C07ped = reader.IsDBNull("C07ped") ? null : reader.GetInt32("C07ped");
                            pedido.Hojasco7ped = reader.IsDBNull("Hojasco7ped") ? null : reader.GetInt32("Hojasco7ped");
                            pedido.C08ped = reader.IsDBNull("C08ped") ? null : reader.GetInt32("C08ped");
                            pedido.Hojasco8ped = reader.IsDBNull("Hojasco8ped") ? null : reader.GetInt32("Hojasco8ped");
                            pedido.C09ped = reader.IsDBNull("C09ped") ? null : reader.GetInt32("C09ped");
                            pedido.Hojasco9ped = reader.IsDBNull("Hojasco9ped") ? null : reader.GetInt32("Hojasco9ped");
                            pedido.Co10ped = reader.IsDBNull("Co10ped") ? null : reader.GetInt32("Co10ped");
                            pedido.Hojasco10ped = reader.IsDBNull("Hojasco10ped") ? null : reader.GetInt32("Hojasco10ped");
                            pedido.Co11ped = reader.IsDBNull("Co11ped") ? null : reader.GetInt32("Co11ped");
                            pedido.Hojasco11ped = reader.IsDBNull("Hojasco11ped") ? null : reader.GetInt32("Hojasco11ped");

                            // Campos Cd (tintas exteriores)
                            pedido.Cd1ped = reader.IsDBNull("Cd1ped") ? null : reader.GetInt32("Cd1ped");
                            pedido.Hojascd1ped = reader.IsDBNull("Hojascd1ped") ? null : reader.GetInt32("Hojascd1ped");
                            pedido.Cd2ped = reader.IsDBNull("Cd2ped") ? null : reader.GetInt32("Cd2ped");
                            pedido.Hojascd2ped = reader.IsDBNull("Hojascd2ped") ? null : reader.GetInt32("Hojascd2ped");
                            pedido.Cd3ped = reader.IsDBNull("Cd3ped") ? null : reader.GetInt32("Cd3ped");
                            pedido.Hojascd3ped = reader.IsDBNull("Hojascd3ped") ? null : reader.GetInt32("Hojascd3ped");
                            pedido.Cd4ped = reader.IsDBNull("Cd4ped") ? null : reader.GetInt32("Cd4ped");
                            pedido.Hojascd4ped = reader.IsDBNull("Hojascd4ped") ? null : reader.GetInt32("Hojascd4ped");

                            // Campos adicionales de PedidoProcesado
                            pedido.AnchoHjlta = reader.IsDBNull("Ancho_hjlta") ? null : reader.GetInt32("Ancho_hjlta");
                            pedido.LargoHjlta = reader.IsDBNull("Largo_hjlta") ? null : reader.GetInt32("Largo_hjlta");
                            pedido.EspesorHjlta = reader.IsDBNull("Espesor_hjlta") ? null : reader.GetInt32("Espesor_hjlta");
                            pedido.TipoHjlta = reader.IsDBNull("Tipo_hjlta") ? null : reader.GetInt32("Tipo_hjlta");
                            pedido.EstadoTintas = reader.IsDBNull("EstadoTintas") ? null : reader.GetString("EstadoTintas");
                            pedido.LineaTintas = reader.IsDBNull("LineaTintas") ? null : reader.GetInt32("LineaTintas");
                            pedido.Obsarticulo = reader.IsDBNull("Obsarticulo") ? null : reader.GetString("Obsarticulo");
                            pedido.Wo = reader.IsDBNull("WO") ? null : reader.GetString("WO");
                            pedido.TipoPedido = reader.IsDBNull("TipoPedido") ? null : reader.GetString("TipoPedido");
                            pedido.FechaFin = reader.IsDBNull("FechaFin") ? null : reader.GetDateTime("FechaFin");
                            pedido.FechaEntregaSolicitada = reader.IsDBNull("FechaEntregaSolicitada") ? null : reader.GetDateTime("FechaEntregaSolicitada");

                            // Campos calculados que devuelve el SP
                            pedido.DatosCliente = reader.IsDBNull("DatosCliente") ? null : reader.GetString("DatosCliente");
                            pedido.NombreLineaTintas = reader.IsDBNull("NombreLineaTintas") ? null : reader.GetString("NombreLineaTintas");
                            pedido.Tinta1Desc = reader.IsDBNull("Tinta1Desc") ? null : reader.GetString("Tinta1Desc");
                            pedido.Tinta2Desc = reader.IsDBNull("Tinta2Desc") ? null : reader.GetString("Tinta2Desc");
                            pedido.Tinta3Desc = reader.IsDBNull("Tinta3Desc") ? null : reader.GetString("Tinta3Desc");
                            pedido.Tinta4Desc = reader.IsDBNull("Tinta4Desc") ? null : reader.GetString("Tinta4Desc");
                            pedido.Tinta5Desc = reader.IsDBNull("Tinta5Desc") ? null : reader.GetString("Tinta5Desc");
                            pedido.Tinta6Desc = reader.IsDBNull("Tinta6Desc") ? null : reader.GetString("Tinta6Desc");
                            pedido.Tinta7Desc = reader.IsDBNull("Tinta7Desc") ? null : reader.GetString("Tinta7Desc");
                            pedido.TintaExt1Desc = reader.IsDBNull("TintaExt1Desc") ? null : reader.GetString("TintaExt1Desc");
                            pedido.TintaExt2Desc = reader.IsDBNull("TintaExt2Desc") ? null : reader.GetString("TintaExt2Desc");
                            pedido.TintaExt3Desc = reader.IsDBNull("TintaExt3Desc") ? null : reader.GetString("TintaExt3Desc");
                            pedido.TintaExt4Desc = reader.IsDBNull("TintaExt4Desc") ? null : reader.GetString("TintaExt4Desc");
                            pedido.CodApli = reader.IsDBNull("CodApli") ? null : reader.GetInt32("CodApli");
                            pedido.Barniz = reader.IsDBNull("Barniz") ? null : reader.GetInt32("Barniz");

                            // Campos que se asignan desde el SP pero se inicializan
                            pedido.Tuberia = reader.IsDBNull("Tuberia") ? false : reader.GetBoolean("Tuberia");
                            pedido.Lote = reader.IsDBNull("Lote") ? null : reader.GetString("Lote");

                            pedidos.Add(pedido);
                        }
                    }

                    var endTime = DateTime.Now;
                    var duration = endTime - startTime;
                    System.Diagnostics.Debug.WriteLine($"[DEBUG] SP ejecutado en {duration.TotalSeconds:F2} segundos, {pedidos.Count} registros devueltos");
                }
            }

            return pedidos;
        }

        private object[] ConstruirArrayParametros(FiltroDTO filtro)
        {
            string listBarnizString = filtro.ListCodBarniz2 != null
                ? string.Join(",", filtro.ListCodBarniz2)
                : null;


            var paramsArray = new SqlParameter[]
            {
                new("@IdCliente", filtro.IdCliente ?? (object)DBNull.Value),
                new("@IdPedido", filtro.IdPedido ?? (object)DBNull.Value),
                new("@Pi1ped", filtro.Pi1ped ?? (object)DBNull.Value),
                new("@Pi2ped", filtro.Pi2ped ?? (object)DBNull.Value),
                new("@Pi3ped", filtro.Pi3ped ?? (object)DBNull.Value),
                new("@Pe1ped", filtro.Pe1ped ?? (object)DBNull.Value),
                new("@Pe2ped", filtro.Pe2ped ?? (object)DBNull.Value),
                new("@Pe3ped", filtro.Pe3ped ?? (object)DBNull.Value),
                new("@FechaPedido", filtro.FechaPedido ?? (object)DBNull.Value),
                new("@FechaMinPedido", filtro.FechaMinPedido ?? (object)DBNull.Value),
                new("@FechaMaxPedido", filtro.FechaMaxPedido ?? (object)DBNull.Value),
                new("@TipoPedido", filtro.TipoPedido ?? (object)DBNull.Value),
                new("@Motivos", filtro.Motivos ?? (object)DBNull.Value),
                new("@TipoElemento", filtro.TipoElemento ?? (object)DBNull.Value),
                new("@Formato", filtro.Formato ?? (object)DBNull.Value),
                new("@Plano", filtro.Plano ?? (object)DBNull.Value),
                new("@AnchoHjlta", filtro.AnchoHjlta ?? (object)DBNull.Value),
                new("@LargoHjlta", filtro.LargoHjlta ?? (object)DBNull.Value),
                new("@EspesorHjlta", filtro.EspesorHjlta ?? (object)DBNull.Value),
                new("@TipoHjlta", filtro.TipoHjlta ?? (object)DBNull.Value),
                new("@EstadoTintas", filtro.EstadoTintas ?? (object)DBNull.Value),
                new("@LineaTintas", filtro.LineaTintas ?? (object)DBNull.Value),
                new("@ObsArticulo", filtro.ObsArt ?? (object)DBNull.Value),
                new("@Tinta1", filtro.Tinta1 ?? (object)DBNull.Value),
                new("@Tinta2", filtro.Tinta2 ?? (object)DBNull.Value),
                new("@Tinta3", filtro.Tinta3 ?? (object)DBNull.Value),
                new("@Tinta4", filtro.Tinta4 ?? (object)DBNull.Value),
                new("@Tinta5", filtro.Tinta5 ?? (object)DBNull.Value),
                new("@Tinta6", filtro.Tinta6 ?? (object)DBNull.Value),
                new("@Tinta7", filtro.Tinta7 ?? (object)DBNull.Value),
                new("@Cd1ped", filtro.Cd1ped ?? (object)DBNull.Value),
                new("@Cd2ped", filtro.Cd2ped ?? (object)DBNull.Value),
                new("@Cd3ped", filtro.Cd3ped ?? (object)DBNull.Value),
                new("@Cd4ped", filtro.Cd4ped ?? (object)DBNull.Value),
                new("@CodApli", filtro.CodApli ?? (object)DBNull.Value),
                new("@CodBarniz", filtro.CodBarniz ?? (object)DBNull.Value),
                new("@LoteBarniz", filtro.LoteBarniz ?? (object)DBNull.Value),
                new("@ListBarniz", listBarnizString ?? (object)DBNull.Value),
                new("@OcultarReprocesos", filtro.OcultarReprocesos),
                new("@SuPedido", filtro.Supedido ?? (object)DBNull.Value),
                new("@WO", filtro.Wo ?? (object)DBNull.Value),
            };
            return paramsArray;
        }
    }
}
